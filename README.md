# 麦克风音量放大器

这是一个用于实时放大麦克风音量的Python程序，特别适用于微信视频通话等场景。

## 功能特点

- 实时音频处理和放大
- 图形化用户界面，操作简单
- 可调节放大倍数（0.1x - 5.0x）
- 自动检测和选择音频设备
- 防止音频溢出和失真

## 安装要求

### 系统要求
- Windows 10
- Python 3.7 或更高版本
- USB麦克风设备

### 依赖包安装

1. 首先确保已安装Python 3.7+
2. 安装依赖包：

```bash
pip install -r requirements.txt
```

**注意**: 在Windows上安装PyAudio可能需要额外步骤：

如果直接安装失败，可以尝试：
```bash
pip install pipwin
pipwin install pyaudio
```

或者下载预编译的wheel文件：
```bash
pip install https://download.lfd.uci.edu/pythonlibs/archived/pyaudio-0.2.11-cp39-cp39-win_amd64.whl
```

## 使用方法

### 1. 运行程序
```bash
python mic_amplifier.py
```

### 2. 配置设备
1. 在"输入设备"下拉菜单中选择你的USB麦克风
2. 在"输出设备"下拉菜单中选择系统默认扬声器
3. 如果设备列表为空，点击"刷新设备"按钮

### 3. 调整设置
1. 使用滑块调整放大倍数（建议2-3倍）
2. 放大倍数过高可能导致音频失真

### 4. 开始使用
1. 点击"开始放大"按钮
2. 程序开始实时处理音频
3. 在微信中进行视频通话时，对方会听到放大后的声音

### 5. 微信设置
1. 打开微信设置 → 通用 → 音视频通话
2. 确保麦克风设备设置为系统默认设备
3. 或者直接选择你正在使用的音频输出设备

## 使用技巧

1. **测试音量**: 可以先用Windows录音机测试放大效果
2. **避免回音**: 使用耳机可以避免扬声器声音被麦克风重新捕获
3. **调整延迟**: 如果感觉有延迟，可以尝试调整CHUNK大小（需要修改代码）
4. **音质优化**: 建议放大倍数不要超过3倍，以保证音质

## 故障排除

### 常见问题

1. **找不到音频设备**
   - 确保USB麦克风已正确连接
   - 在Windows设备管理器中检查音频设备
   - 重启程序并点击"刷新设备"

2. **音频失真或噪音**
   - 降低放大倍数
   - 检查麦克风是否正常工作
   - 确保没有其他程序占用音频设备

3. **程序无法启动**
   - 检查Python版本是否正确
   - 确保所有依赖包已安装
   - 查看错误信息并相应处理

4. **微信中听不到放大效果**
   - 确保程序正在运行（状态显示"运行中"）
   - 检查微信音频设置
   - 尝试重启微信

### 性能优化

如果遇到性能问题，可以尝试：
1. 调整CHUNK大小（在代码中修改）
2. 降低采样率（在代码中修改RATE）
3. 关闭其他占用音频的程序

## 技术说明

- 使用PyAudio进行实时音频处理
- 采用16位PCM音频格式
- 默认采样率44.1kHz
- 单声道处理以减少延迟

## 注意事项

1. 程序运行时会占用麦克风设备，其他程序可能无法同时使用
2. 放大倍数过高可能导致音频削波和失真
3. 建议在安静环境中使用，避免背景噪音被放大
4. 长时间使用建议定期重启程序以释放资源

## 许可证

本程序仅供个人学习和使用，请勿用于商业用途。
