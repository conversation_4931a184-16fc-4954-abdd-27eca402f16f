@echo off
chcp 65001 >nul
echo ================================
echo 麦克风音量放大器 - 完整环境安装
echo ================================
echo.

echo 正在检查Python安装...
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo 未检测到Python，开始自动安装...
        goto install_python
    ) else (
        echo 检测到Python Launcher，使用py命令
        set PYTHON_CMD=py
        goto check_pip
    )
) else (
    echo 检测到Python，使用python命令
    set PYTHON_CMD=python
    goto check_pip
)

:install_python
echo.
echo ================================
echo 自动安装Python 3.11
echo ================================
echo.

echo 正在下载Python 3.11安装包...
echo 这可能需要几分钟时间，请耐心等待...

:: 创建临时目录
if not exist "%TEMP%\python_installer" mkdir "%TEMP%\python_installer"
cd /d "%TEMP%\python_installer"

:: 下载Python 3.11
echo 下载Python安装包...
powershell -Command "& {[Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile 'python-installer.exe'}"

if not exist "python-installer.exe" (
    echo 下载失败！请检查网络连接或手动安装Python
    echo 手动下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo 开始安装Python...
echo 安装选项: 添加到PATH, 安装pip, 安装所有用户
python-installer.exe /quiet InstallAllUsers=1 PrependPath=1 Include_test=0

echo 等待安装完成...
timeout /t 30 /nobreak >nul

:: 刷新环境变量
call refreshenv >nul 2>&1

:: 验证安装
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo Python安装可能失败，请重启命令提示符后重试
        echo 或手动安装Python: https://www.python.org/downloads/
        pause
        exit /b 1
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

echo Python安装成功！
cd /d "%~dp0"

:check_pip
echo.
echo 检查pip...
%PYTHON_CMD% -m pip --version >nul 2>&1
if errorlevel 1 (
    echo 安装pip...
    %PYTHON_CMD% -m ensurepip --upgrade
)

echo.
echo ================================
echo 安装项目依赖包
echo ================================
echo.

echo 升级pip...
%PYTHON_CMD% -m pip install --upgrade pip

echo.
echo 安装numpy...
%PYTHON_CMD% -m pip install numpy

echo.
echo 安装PyAudio...
%PYTHON_CMD% -m pip install pyaudio
if errorlevel 1 (
    echo PyAudio直接安装失败，尝试其他方法...

    echo 尝试安装pipwin...
    %PYTHON_CMD% -m pip install pipwin
    if not errorlevel 1 (
        echo 使用pipwin安装PyAudio...
        %PYTHON_CMD% -m pipwin install pyaudio
    )

    if errorlevel 1 (
        echo 尝试从预编译包安装PyAudio...
        %PYTHON_CMD% -m pip install --only-binary=all pyaudio

        if errorlevel 1 (
            echo.
            echo 警告: PyAudio安装失败！
            echo 这可能是因为缺少Microsoft Visual C++ 构建工具
            echo.
            echo 解决方案:
            echo 1. 安装Microsoft Visual C++ Build Tools
            echo 2. 或下载预编译的PyAudio包
            echo.
            echo 是否继续？程序可能无法正常工作 (y/n)
            set /p continue="请选择: "
            if /i not "%continue%"=="y" (
                echo 安装中止
                pause
                exit /b 1
            )
        )
    )
)

echo.
echo 安装tkinter支持...
%PYTHON_CMD% -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo 警告: tkinter未安装，图形界面可能无法使用
    echo 请确保Python安装时包含了tkinter模块
)

echo.
echo ================================
echo 环境安装完成！
echo ================================
echo.

:menu
echo 请选择要运行的程序:
echo 1. 图形界面版本 (推荐)
echo 2. 命令行简化版本
echo 3. 测试环境
echo 4. 退出
echo ================================
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" (
    echo 启动图形界面版本...
    %PYTHON_CMD% mic_amplifier.py
    if errorlevel 1 (
        echo 程序运行出错，按任意键返回菜单...
        pause >nul
    )
    goto menu
) else if "%choice%"=="2" (
    echo 启动命令行版本...
    %PYTHON_CMD% simple_mic_amplifier.py
    if errorlevel 1 (
        echo 程序运行出错，按任意键返回菜单...
        pause >nul
    )
    goto menu
) else if "%choice%"=="3" (
    echo 测试Python环境...
    echo.
    echo Python版本:
    %PYTHON_CMD% --version
    echo.
    echo 测试导入模块:
    %PYTHON_CMD% -c "import numpy; print('numpy: OK')"
    %PYTHON_CMD% -c "import pyaudio; print('pyaudio: OK')"
    %PYTHON_CMD% -c "import tkinter; print('tkinter: OK')"
    echo.
    echo 测试完成！
    pause
    goto menu
) else if "%choice%"=="4" (
    echo 再见！
    exit /b 0
) else (
    echo 无效选择，请重新输入
    goto menu
)

pause
