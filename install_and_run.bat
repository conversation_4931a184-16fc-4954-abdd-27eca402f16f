@echo off
chcp 65001 >nul
echo ================================
echo 麦克风音量放大器 - 安装和运行
echo ================================
echo.

echo 正在检查Python安装...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo Python已安装
echo.

echo 正在安装依赖包...
echo.

echo 尝试安装PyAudio...
pip install pyaudio >nul 2>&1
if errorlevel 1 (
    echo PyAudio安装失败，尝试使用pipwin...
    pip install pipwin
    pipwin install pyaudio
    if errorlevel 1 (
        echo 警告: PyAudio安装可能失败，请手动安装
        echo 可以尝试: pip install https://download.lfd.uci.edu/pythonlibs/archived/pyaudio-0.2.11-cp39-cp39-win_amd64.whl
    )
)

echo 安装numpy...
pip install numpy

echo.
echo 依赖包安装完成！
echo.

:menu
echo ================================
echo 请选择要运行的程序:
echo 1. 图形界面版本 (推荐)
echo 2. 命令行简化版本
echo 3. 退出
echo ================================
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo 启动图形界面版本...
    python mic_amplifier.py
    goto menu
) else if "%choice%"=="2" (
    echo 启动命令行版本...
    python simple_mic_amplifier.py
    goto menu
) else if "%choice%"=="3" (
    echo 再见！
    exit /b 0
) else (
    echo 无效选择，请重新输入
    goto menu
)

pause
