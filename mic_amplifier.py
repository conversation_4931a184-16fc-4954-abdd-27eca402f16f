#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
麦克风音量放大器
实时捕获麦克风音频并放大后输出到虚拟音频设备
适用于微信视频通话等场景
"""

import pyaudio
import numpy as np
import threading
import time
import tkinter as tk
from tkinter import ttk, messagebox
import sys

class MicAmplifier:
    def __init__(self):
        self.CHUNK = 1024  # 音频块大小
        self.FORMAT = pyaudio.paInt16  # 16位音频
        self.CHANNELS = 1  # 单声道
        self.RATE = 44100  # 采样率
        
        self.amplification_factor = 2.0  # 放大倍数
        self.is_running = False
        self.audio = None
        self.input_stream = None
        self.output_stream = None
        
        # 音频设备索引
        self.input_device_index = None
        self.output_device_index = None
        
        self.setup_audio()
        
    def setup_audio(self):
        """初始化音频设备"""
        try:
            self.audio = pyaudio.PyAudio()
            self.list_audio_devices()
        except Exception as e:
            print(f"音频初始化失败: {e}")
            
    def list_audio_devices(self):
        """列出所有音频设备"""
        print("可用的音频设备:")
        print("-" * 50)
        
        for i in range(self.audio.get_device_count()):
            device_info = self.audio.get_device_info_by_index(i)
            print(f"设备 {i}: {device_info['name']}")
            print(f"  最大输入声道: {device_info['maxInputChannels']}")
            print(f"  最大输出声道: {device_info['maxOutputChannels']}")
            print(f"  默认采样率: {device_info['defaultSampleRate']}")
            print("-" * 30)
            
    def set_devices(self, input_index=None, output_index=None):
        """设置输入输出设备"""
        if input_index is not None:
            self.input_device_index = input_index
        if output_index is not None:
            self.output_device_index = output_index
            
    def audio_callback(self, in_data, frame_count, time_info, status):
        """音频回调函数"""
        try:
            # 将字节数据转换为numpy数组
            audio_data = np.frombuffer(in_data, dtype=np.int16)
            
            # 应用放大
            amplified_data = audio_data * self.amplification_factor
            
            # 防止溢出
            amplified_data = np.clip(amplified_data, -32768, 32767)
            
            # 转换回字节
            output_data = amplified_data.astype(np.int16).tobytes()
            
            return (output_data, pyaudio.paContinue)
        except Exception as e:
            print(f"音频处理错误: {e}")
            return (in_data, pyaudio.paContinue)
            
    def start_amplification(self):
        """开始音频放大"""
        if self.is_running:
            return
            
        try:
            # 打开输入流
            self.input_stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                input_device_index=self.input_device_index,
                frames_per_buffer=self.CHUNK,
                stream_callback=self.audio_callback
            )
            
            # 打开输出流
            self.output_stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                output=True,
                output_device_index=self.output_device_index,
                frames_per_buffer=self.CHUNK
            )
            
            self.input_stream.start_stream()
            self.is_running = True
            print("麦克风放大已启动")
            
        except Exception as e:
            print(f"启动失败: {e}")
            messagebox.showerror("错误", f"启动失败: {e}")
            
    def stop_amplification(self):
        """停止音频放大"""
        if not self.is_running:
            return
            
        self.is_running = False
        
        if self.input_stream:
            self.input_stream.stop_stream()
            self.input_stream.close()
            
        if self.output_stream:
            self.output_stream.stop_stream()
            self.output_stream.close()
            
        print("麦克风放大已停止")
        
    def set_amplification(self, factor):
        """设置放大倍数"""
        self.amplification_factor = max(0.1, min(10.0, factor))
        print(f"放大倍数设置为: {self.amplification_factor:.1f}")
        
    def cleanup(self):
        """清理资源"""
        self.stop_amplification()
        if self.audio:
            self.audio.terminate()

class MicAmplifierGUI:
    def __init__(self):
        self.amplifier = MicAmplifier()
        self.setup_gui()
        
    def setup_gui(self):
        """设置图形界面"""
        self.root = tk.Tk()
        self.root.title("麦克风音量放大器")
        self.root.geometry("500x400")
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 设备选择
        ttk.Label(main_frame, text="输入设备 (麦克风):").grid(row=0, column=0, sticky=tk.W, pady=5)
        self.input_combo = ttk.Combobox(main_frame, width=50)
        self.input_combo.grid(row=0, column=1, pady=5, padx=(10, 0))
        
        ttk.Label(main_frame, text="输出设备 (扬声器):").grid(row=1, column=0, sticky=tk.W, pady=5)
        self.output_combo = ttk.Combobox(main_frame, width=50)
        self.output_combo.grid(row=1, column=1, pady=5, padx=(10, 0))
        
        # 刷新设备按钮
        ttk.Button(main_frame, text="刷新设备", command=self.refresh_devices).grid(row=2, column=1, pady=10, sticky=tk.E)
        
        # 放大倍数控制
        ttk.Label(main_frame, text="放大倍数:").grid(row=3, column=0, sticky=tk.W, pady=5)
        self.amp_scale = ttk.Scale(main_frame, from_=0.1, to=5.0, orient=tk.HORIZONTAL, length=300)
        self.amp_scale.set(2.0)
        self.amp_scale.grid(row=3, column=1, pady=5, padx=(10, 0), sticky=(tk.W, tk.E))
        self.amp_scale.configure(command=self.on_amplification_change)
        
        self.amp_label = ttk.Label(main_frame, text="2.0x")
        self.amp_label.grid(row=4, column=1, sticky=tk.W, padx=(10, 0))
        
        # 控制按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=5, column=0, columnspan=2, pady=20)
        
        self.start_button = ttk.Button(button_frame, text="开始放大", command=self.start_amplification)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="停止放大", command=self.stop_amplification, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        # 状态显示
        self.status_label = ttk.Label(main_frame, text="状态: 未启动", foreground="red")
        self.status_label.grid(row=6, column=0, columnspan=2, pady=10)
        
        # 说明文本
        info_text = """
使用说明:
1. 选择你的USB麦克风作为输入设备
2. 选择系统默认扬声器作为输出设备
3. 调整放大倍数 (建议2-3倍)
4. 点击"开始放大"
5. 在微信中将麦克风设备设置为系统默认设备
6. 进行视频通话时，声音会被自动放大

注意: 放大倍数过高可能导致音频失真
        """
        
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="10")
        info_frame.grid(row=7, column=0, columnspan=2, pady=10, sticky=(tk.W, tk.E))
        
        ttk.Label(info_frame, text=info_text, justify=tk.LEFT).pack()
        
        # 初始化设备列表
        self.refresh_devices()
        
    def refresh_devices(self):
        """刷新音频设备列表"""
        try:
            devices = []
            input_devices = []
            output_devices = []
            
            for i in range(self.amplifier.audio.get_device_count()):
                device_info = self.amplifier.audio.get_device_info_by_index(i)
                device_name = f"{i}: {device_info['name']}"
                
                if device_info['maxInputChannels'] > 0:
                    input_devices.append(device_name)
                if device_info['maxOutputChannels'] > 0:
                    output_devices.append(device_name)
            
            self.input_combo['values'] = input_devices
            self.output_combo['values'] = output_devices
            
            # 尝试自动选择USB设备
            for device in input_devices:
                if 'usb' in device.lower() or 'microphone' in device.lower():
                    self.input_combo.set(device)
                    break
                    
            # 选择默认输出设备
            if output_devices:
                self.output_combo.set(output_devices[0])
                
        except Exception as e:
            messagebox.showerror("错误", f"刷新设备失败: {e}")
            
    def on_amplification_change(self, value):
        """放大倍数改变回调"""
        amp_value = float(value)
        self.amp_label.config(text=f"{amp_value:.1f}x")
        self.amplifier.set_amplification(amp_value)
        
    def start_amplification(self):
        """开始放大"""
        try:
            # 获取选择的设备索引
            input_selection = self.input_combo.get()
            output_selection = self.output_combo.get()
            
            if not input_selection or not output_selection:
                messagebox.showwarning("警告", "请选择输入和输出设备")
                return
                
            input_index = int(input_selection.split(':')[0])
            output_index = int(output_selection.split(':')[0])
            
            self.amplifier.set_devices(input_index, output_index)
            self.amplifier.start_amplification()
            
            self.start_button.config(state=tk.DISABLED)
            self.stop_button.config(state=tk.NORMAL)
            self.status_label.config(text="状态: 运行中", foreground="green")
            
        except Exception as e:
            messagebox.showerror("错误", f"启动失败: {e}")
            
    def stop_amplification(self):
        """停止放大"""
        self.amplifier.stop_amplification()
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="状态: 已停止", foreground="red")
        
    def on_closing(self):
        """窗口关闭事件"""
        self.amplifier.cleanup()
        self.root.destroy()
        
    def run(self):
        """运行GUI"""
        self.root.mainloop()

def main():
    """主函数"""
    try:
        app = MicAmplifierGUI()
        app.run()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")

if __name__ == "__main__":
    main()
