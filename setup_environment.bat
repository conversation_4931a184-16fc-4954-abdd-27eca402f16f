@echo off
chcp 65001 >nul
title Python环境安装器

echo ========================================
echo Python环境自动安装器
echo ========================================
echo.

:: 检查管理员权限
net session >nul 2>&1
if errorlevel 1 (
    echo 建议以管理员身份运行此脚本以获得最佳安装体验
    echo 是否继续？(y/n)
    set /p admin_choice="请选择: "
    if /i not "%admin_choice%"=="y" exit /b 1
)

echo 步骤1: 检查Python安装状态...
echo.

:: 检查Python是否已安装
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ 检测到python命令
    python --version
    set PYTHON_CMD=python
    goto check_modules
)

py --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ 检测到py命令
    py --version
    set PYTHON_CMD=py
    goto check_modules
)

echo ✗ 未检测到Python安装
echo.

:install_python
echo 步骤2: 安装Python 3.11...
echo.

echo 选择安装方式:
echo 1. 自动下载并安装 (推荐)
echo 2. 手动安装指导
echo 3. 跳过Python安装
set /p install_choice="请选择 (1-3): "

if "%install_choice%"=="2" goto manual_install
if "%install_choice%"=="3" goto check_modules

echo 正在下载Python 3.11.7...
echo 下载地址: https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe
echo.

:: 创建下载目录
set DOWNLOAD_DIR=%TEMP%\python_setup
if not exist "%DOWNLOAD_DIR%" mkdir "%DOWNLOAD_DIR%"

:: 使用PowerShell下载
echo 开始下载，请稍候...
powershell -Command "try { [Net.ServicePointManager]::SecurityProtocol = [Net.SecurityProtocolType]::Tls12; $ProgressPreference = 'SilentlyContinue'; Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.11.7/python-3.11.7-amd64.exe' -OutFile '%DOWNLOAD_DIR%\python-installer.exe' -UseBasicParsing; Write-Host 'Download completed successfully' } catch { Write-Host 'Download failed:' $_.Exception.Message; exit 1 }"

if not exist "%DOWNLOAD_DIR%\python-installer.exe" (
    echo 下载失败！请检查网络连接
    goto manual_install
)

echo 下载完成！开始安装Python...
echo.
echo 安装选项:
echo - 添加Python到系统PATH
echo - 安装pip包管理器
echo - 为所有用户安装
echo.

"%DOWNLOAD_DIR%\python-installer.exe" /quiet InstallAllUsers=1 PrependPath=1 Include_test=0 Include_doc=0

echo 等待安装完成...
timeout /t 20 /nobreak >nul

:: 刷新PATH环境变量
set PATH=%PATH%;%LOCALAPPDATA%\Programs\Python\Python311;%LOCALAPPDATA%\Programs\Python\Python311\Scripts
set PATH=%PATH%;C:\Program Files\Python311;C:\Program Files\Python311\Scripts

echo 验证Python安装...
python --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ Python安装成功！
    python --version
    set PYTHON_CMD=python
    goto check_modules
)

py --version >nul 2>&1
if not errorlevel 1 (
    echo ✓ Python安装成功！
    py --version
    set PYTHON_CMD=py
    goto check_modules
)

echo ✗ Python安装验证失败
echo 请重启命令提示符后重试，或手动安装Python
goto manual_install

:manual_install
echo.
echo ========================================
echo 手动安装指导
echo ========================================
echo.
echo 请按以下步骤手动安装Python:
echo.
echo 1. 打开浏览器访问: https://www.python.org/downloads/
echo 2. 下载Python 3.11或更高版本 (Windows x86-64 executable installer)
echo 3. 运行安装程序
echo 4. 重要: 勾选 "Add Python to PATH" 选项
echo 5. 选择 "Install Now" 或自定义安装
echo 6. 安装完成后重启此脚本
echo.
echo 按任意键打开Python官网...
pause >nul
start https://www.python.org/downloads/
echo.
echo 安装完成后请重新运行此脚本
pause
exit /b 1

:check_modules
echo.
echo 步骤3: 检查和安装Python模块...
echo.

echo 检查pip...
%PYTHON_CMD% -m pip --version >nul 2>&1
if errorlevel 1 (
    echo 安装pip...
    %PYTHON_CMD% -m ensurepip --upgrade
)

echo ✓ pip可用
echo.

echo 升级pip到最新版本...
%PYTHON_CMD% -m pip install --upgrade pip --quiet

echo 安装必需模块...
echo.

echo [1/2] 安装numpy...
%PYTHON_CMD% -m pip install numpy --quiet
if errorlevel 1 (
    echo ✗ numpy安装失败
    set INSTALL_ERROR=1
) else (
    echo ✓ numpy安装成功
)

echo [2/2] 安装pyaudio...
%PYTHON_CMD% -m pip install pyaudio --quiet
if errorlevel 1 (
    echo ✗ pyaudio直接安装失败，尝试其他方法...
    
    echo 尝试pipwin方法...
    %PYTHON_CMD% -m pip install pipwin --quiet
    %PYTHON_CMD% -m pipwin install pyaudio --quiet
    
    if errorlevel 1 (
        echo ✗ pyaudio安装失败
        echo.
        echo PyAudio安装失败的可能原因:
        echo 1. 缺少Microsoft Visual C++ 构建工具
        echo 2. 需要预编译的wheel包
        echo.
        echo 解决方案:
        echo 1. 安装Microsoft Visual C++ Build Tools
        echo 2. 或访问: https://www.lfd.uci.edu/~gohlke/pythonlibs/#pyaudio
        echo    下载对应版本的.whl文件手动安装
        echo.
        set INSTALL_ERROR=1
    ) else (
        echo ✓ pyaudio安装成功 (通过pipwin)
    )
) else (
    echo ✓ pyaudio安装成功
)

echo.
echo 检查tkinter (图形界面支持)...
%PYTHON_CMD% -c "import tkinter" >nul 2>&1
if errorlevel 1 (
    echo ✗ tkinter不可用，图形界面可能无法使用
    echo 请确保Python安装时包含了tkinter模块
    set INSTALL_ERROR=1
) else (
    echo ✓ tkinter可用
)

echo.
echo ========================================
echo 环境检查完成
echo ========================================
echo.

if defined INSTALL_ERROR (
    echo ⚠️  部分模块安装失败，但可以尝试运行程序
) else (
    echo ✅ 所有组件安装成功！
)

echo.
echo 环境信息:
%PYTHON_CMD% --version
%PYTHON_CMD% -c "import sys; print(f'Python路径: {sys.executable}')"
echo.

echo 是否现在运行麦克风放大器？(y/n)
set /p run_choice="请选择: "
if /i "%run_choice%"=="y" (
    if exist "mic_amplifier.py" (
        echo 启动图形界面版本...
        %PYTHON_CMD% mic_amplifier.py
    ) else (
        echo 未找到程序文件，请确保在正确的目录中运行
    )
)

echo.
echo 安装完成！你现在可以运行 install_and_run.bat 来使用程序
pause
