#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版麦克风音量放大器
命令行版本，更轻量级
"""

import pyaudio
import numpy as np
import threading
import time

class SimpleMicAmplifier:
    def __init__(self, amplification_factor=2.0):
        self.CHUNK = 1024
        self.FORMAT = pyaudio.paInt16
        self.CHANNELS = 1
        self.RATE = 44100
        
        self.amplification_factor = amplification_factor
        self.is_running = False
        
        self.audio = pyaudio.PyAudio()
        
    def list_devices(self):
        """列出所有音频设备"""
        print("可用的音频设备:")
        print("=" * 60)
        
        input_devices = []
        output_devices = []
        
        for i in range(self.audio.get_device_count()):
            device_info = self.audio.get_device_info_by_index(i)
            device_name = device_info['name']
            
            if device_info['maxInputChannels'] > 0:
                input_devices.append((i, device_name))
                print(f"输入设备 {i}: {device_name}")
                
            if device_info['maxOutputChannels'] > 0:
                output_devices.append((i, device_name))
                print(f"输出设备 {i}: {device_name}")
                
        print("=" * 60)
        return input_devices, output_devices
        
    def start_amplification(self, input_device_index=None, output_device_index=None):
        """开始音频放大"""
        try:
            print(f"开始音频放大，放大倍数: {self.amplification_factor}x")
            
            def audio_callback(in_data, frame_count, time_info, status):
                # 转换为numpy数组
                audio_data = np.frombuffer(in_data, dtype=np.int16)
                
                # 应用放大
                amplified_data = audio_data * self.amplification_factor
                
                # 防止溢出
                amplified_data = np.clip(amplified_data, -32768, 32767)
                
                # 转换回字节
                return (amplified_data.astype(np.int16).tobytes(), pyaudio.paContinue)
            
            # 创建音频流
            self.stream = self.audio.open(
                format=self.FORMAT,
                channels=self.CHANNELS,
                rate=self.RATE,
                input=True,
                output=True,
                input_device_index=input_device_index,
                output_device_index=output_device_index,
                frames_per_buffer=self.CHUNK,
                stream_callback=audio_callback
            )
            
            self.stream.start_stream()
            self.is_running = True
            
            print("音频放大已启动！按 Ctrl+C 停止...")
            
            # 保持程序运行
            while self.stream.is_active() and self.is_running:
                time.sleep(0.1)
                
        except Exception as e:
            print(f"启动失败: {e}")
            
    def stop(self):
        """停止音频放大"""
        self.is_running = False
        if hasattr(self, 'stream'):
            self.stream.stop_stream()
            self.stream.close()
        self.audio.terminate()
        print("音频放大已停止")

def main():
    """主函数"""
    print("简化版麦克风音量放大器")
    print("=" * 40)
    
    amplifier = SimpleMicAmplifier()
    
    try:
        # 列出设备
        input_devices, output_devices = amplifier.list_devices()
        
        # 让用户选择设备
        print("\n请选择输入设备 (麦克风):")
        input_choice = input("输入设备编号 (直接回车使用默认): ").strip()
        input_device_index = int(input_choice) if input_choice else None
        
        print("\n请选择输出设备 (扬声器):")
        output_choice = input("输入设备编号 (直接回车使用默认): ").strip()
        output_device_index = int(output_choice) if output_choice else None
        
        # 设置放大倍数
        print("\n请设置放大倍数:")
        amp_input = input("输入放大倍数 (1.0-5.0, 默认2.0): ").strip()
        if amp_input:
            try:
                amp_factor = float(amp_input)
                amp_factor = max(1.0, min(5.0, amp_factor))
                amplifier.amplification_factor = amp_factor
            except ValueError:
                print("无效输入，使用默认值 2.0")
        
        print(f"\n配置完成:")
        print(f"输入设备: {input_device_index if input_device_index is not None else '默认'}")
        print(f"输出设备: {output_device_index if output_device_index is not None else '默认'}")
        print(f"放大倍数: {amplifier.amplification_factor}x")
        print("\n按回车开始...")
        input()
        
        # 开始放大
        amplifier.start_amplification(input_device_index, output_device_index)
        
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序运行错误: {e}")
    finally:
        amplifier.stop()

if __name__ == "__main__":
    main()
