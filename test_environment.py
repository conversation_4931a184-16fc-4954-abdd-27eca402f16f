#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Python环境测试脚本
检查所有必需的模块是否正确安装
"""

import sys
import platform

def test_python_version():
    """测试Python版本"""
    print("=" * 50)
    print("Python环境测试")
    print("=" * 50)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.architecture()[0]}")
    print()
    
    # 检查Python版本
    if sys.version_info < (3, 7):
        print("❌ Python版本过低，需要3.7或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def test_module(module_name, import_name=None):
    """测试模块导入"""
    if import_name is None:
        import_name = module_name
        
    try:
        module = __import__(import_name)
        version = getattr(module, '__version__', '未知版本')
        print(f"✅ {module_name}: {version}")
        return True
    except ImportError as e:
        print(f"❌ {module_name}: 导入失败 - {e}")
        return False
    except Exception as e:
        print(f"⚠️  {module_name}: 导入异常 - {e}")
        return False

def test_audio_devices():
    """测试音频设备"""
    try:
        import pyaudio
        
        print("\n" + "=" * 30)
        print("音频设备测试")
        print("=" * 30)
        
        audio = pyaudio.PyAudio()
        device_count = audio.get_device_count()
        
        print(f"检测到 {device_count} 个音频设备:")
        print()
        
        input_devices = []
        output_devices = []
        
        for i in range(device_count):
            try:
                device_info = audio.get_device_info_by_index(i)
                device_name = device_info['name']
                max_input = device_info['maxInputChannels']
                max_output = device_info['maxOutputChannels']
                
                print(f"设备 {i}: {device_name}")
                print(f"  输入声道: {max_input}")
                print(f"  输出声道: {max_output}")
                print(f"  默认采样率: {device_info['defaultSampleRate']}")
                
                if max_input > 0:
                    input_devices.append((i, device_name))
                if max_output > 0:
                    output_devices.append((i, device_name))
                    
                print()
                
            except Exception as e:
                print(f"设备 {i}: 获取信息失败 - {e}")
        
        print(f"可用输入设备: {len(input_devices)} 个")
        print(f"可用输出设备: {len(output_devices)} 个")
        
        audio.terminate()
        
        if len(input_devices) == 0:
            print("❌ 未找到可用的输入设备（麦克风）")
            return False
        if len(output_devices) == 0:
            print("❌ 未找到可用的输出设备（扬声器）")
            return False
            
        print("✅ 音频设备检测正常")
        return True
        
    except Exception as e:
        print(f"❌ 音频设备测试失败: {e}")
        return False

def test_gui():
    """测试GUI支持"""
    try:
        import tkinter as tk
        
        print("\n" + "=" * 30)
        print("图形界面测试")
        print("=" * 30)
        
        # 创建一个简单的测试窗口
        root = tk.Tk()
        root.withdraw()  # 隐藏窗口
        
        # 测试基本组件
        frame = tk.Frame(root)
        label = tk.Label(frame, text="测试")
        button = tk.Button(frame, text="测试按钮")
        
        print("✅ tkinter基本组件测试通过")
        
        # 测试ttk
        try:
            from tkinter import ttk
            combo = ttk.Combobox(frame)
            scale = ttk.Scale(frame)
            print("✅ ttk组件测试通过")
        except Exception as e:
            print(f"⚠️  ttk组件测试失败: {e}")
        
        root.destroy()
        return True
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        return False

def test_audio_processing():
    """测试音频处理功能"""
    try:
        import numpy as np
        import pyaudio
        
        print("\n" + "=" * 30)
        print("音频处理测试")
        print("=" * 30)
        
        # 测试numpy数组操作
        test_data = np.array([1, 2, 3, 4, 5], dtype=np.int16)
        amplified = test_data * 2.0
        clipped = np.clip(amplified, -32768, 32767)
        
        print("✅ numpy音频数据处理测试通过")
        
        # 测试PyAudio基本功能
        audio = pyaudio.PyAudio()
        
        # 测试音频格式支持
        formats = [
            (pyaudio.paInt16, "16位整数"),
            (pyaudio.paFloat32, "32位浮点"),
        ]
        
        for fmt, name in formats:
            try:
                # 这里只是测试格式是否被支持，不实际打开流
                print(f"✅ 支持音频格式: {name}")
            except:
                print(f"⚠️  不支持音频格式: {name}")
        
        audio.terminate()
        print("✅ 音频处理功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 音频处理测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始环境测试...\n")
    
    tests = [
        ("Python版本", test_python_version),
        ("numpy模块", lambda: test_module("numpy")),
        ("pyaudio模块", lambda: test_module("pyaudio")),
        ("tkinter模块", lambda: test_module("tkinter")),
        ("音频设备", test_audio_devices),
        ("图形界面", test_gui),
        ("音频处理", test_audio_processing),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试出现异常: {e}")
            results.append((test_name, False))
        print()
    
    # 总结
    print("=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print()
    print(f"总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！环境配置完美！")
        print("你可以正常使用麦克风放大器程序了。")
    elif passed >= total - 1:
        print("⚠️  大部分测试通过，程序应该可以正常运行。")
    else:
        print("❌ 多项测试失败，请检查环境配置。")
        print("\n建议:")
        print("1. 重新运行 setup_environment.bat")
        print("2. 检查Python和模块安装")
        print("3. 确保音频设备正常连接")
    
    print("\n按回车键退出...")
    input()

if __name__ == "__main__":
    main()
