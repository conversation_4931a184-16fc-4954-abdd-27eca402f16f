@echo off
chcp 65001 >nul
title 麦克风音量放大器 - 快速开始

echo ========================================
echo 麦克风音量放大器 - 快速开始
echo ========================================
echo.

echo 欢迎使用麦克风音量放大器！
echo.
echo 这个程序可以帮你放大麦克风音量，
echo 让微信视频通话时对方听到更清晰的声音。
echo.

echo 请选择操作:
echo.
echo 1. 🔧 首次使用 - 安装Python环境
echo 2. 🧪 测试环境 - 检查安装状态  
echo 3. 🎤 启动程序 - 开始使用
echo 4. 📖 查看说明 - 使用帮助
echo 5. ❌ 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto install_env
if "%choice%"=="2" goto test_env
if "%choice%"=="3" goto run_program
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit
goto invalid_choice

:install_env
echo.
echo 🔧 开始安装Python环境...
echo.
if exist "setup_environment.bat" (
    call setup_environment.bat
) else (
    echo 错误: 找不到 setup_environment.bat 文件
    echo 请确保所有文件都在同一目录中
    pause
)
goto main_menu

:test_env
echo.
echo 🧪 开始测试环境...
echo.

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 未检测到Python，请先安装环境
        echo.
        echo 是否现在安装？(y/n)
        set /p install_now="请选择: "
        if /i "%install_now%"=="y" goto install_env
        goto main_menu
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

if exist "test_environment.py" (
    %PYTHON_CMD% test_environment.py
) else (
    echo 错误: 找不到 test_environment.py 文件
    pause
)
goto main_menu

:run_program
echo.
echo 🎤 启动麦克风放大器...
echo.

:: 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    py --version >nul 2>&1
    if errorlevel 1 (
        echo ❌ 未检测到Python，请先安装环境
        goto main_menu
    ) else (
        set PYTHON_CMD=py
    )
) else (
    set PYTHON_CMD=python
)

if exist "install_and_run.bat" (
    call install_and_run.bat
) else (
    echo 错误: 找不到 install_and_run.bat 文件
    pause
)
goto main_menu

:show_help
echo.
echo 📖 使用说明
echo ========================================
echo.
echo 🎯 程序功能:
echo   - 实时放大麦克风音量
echo   - 适用于微信视频通话
echo   - 图形界面，操作简单
echo.
echo 🔧 使用步骤:
echo   1. 首次使用请选择"安装Python环境"
echo   2. 连接USB麦克风到电脑
echo   3. 运行程序并选择音频设备
echo   4. 调整放大倍数(建议2-3倍)
echo   5. 点击"开始放大"
echo   6. 在微信中进行视频通话
echo.
echo 💡 使用技巧:
echo   - 建议使用耳机避免回音
echo   - 放大倍数不要过高避免失真
echo   - 程序运行时会占用麦克风设备
echo.
echo ⚠️  注意事项:
echo   - 确保USB麦克风正常连接
echo   - 微信中选择正确的音频设备
echo   - 在安静环境中使用效果更好
echo.
echo 🆘 常见问题:
echo   - 找不到设备: 检查USB连接，点击刷新
echo   - 声音失真: 降低放大倍数
echo   - 程序无法启动: 重新安装Python环境
echo.
echo 按任意键返回主菜单...
pause >nul
goto main_menu

:invalid_choice
echo.
echo ❌ 无效选择，请重新输入
echo.
timeout /t 2 /nobreak >nul

:main_menu
cls
echo ========================================
echo 麦克风音量放大器 - 快速开始
echo ========================================
echo.

echo 请选择操作:
echo.
echo 1. 🔧 首次使用 - 安装Python环境
echo 2. 🧪 测试环境 - 检查安装状态  
echo 3. 🎤 启动程序 - 开始使用
echo 4. 📖 查看说明 - 使用帮助
echo 5. ❌ 退出
echo.

set /p choice="请输入选择 (1-5): "

if "%choice%"=="1" goto install_env
if "%choice%"=="2" goto test_env
if "%choice%"=="3" goto run_program
if "%choice%"=="4" goto show_help
if "%choice%"=="5" goto exit
goto invalid_choice

:exit
echo.
echo 👋 感谢使用麦克风音量放大器！
echo.
timeout /t 2 /nobreak >nul
exit /b 0
